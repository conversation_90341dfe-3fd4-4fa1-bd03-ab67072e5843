var authRestApiProxy = require("./authRestApiProxy");
var fs = require('fs');
var path = require('path');
var log4js = require('./log4jsUtil.js');
var logger = log4js.getLogger('sitemap-nodejs');

var reptile = {
    /**
     * 请求java后台接口生成sitemap
     */
    genSitemap: function () {
        authRestApiProxy.get('RetailerService', "/seo/all", function resultData(err, data) {
            if (data != null && err == null) {
                var save_path = path.resolve(__dirname,'../public/dest/sitemap.xml');//文件保存路径
                xmlns = 'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd"';
                var smContent = '<urlset ' + xmlns + '>\n';
                data.forEach(function (item) {
                    smContent += '  <url>\n';
                    smContent += '    <loc><![CDATA[' + item.loc + ']]></loc>\n';
                    smContent += '    <priority>' + item.priority + '</priority>\n';
                    smContent += '    <lastmod>' + item.lastmod + '</lastmod>\n';
                    smContent += '    <changefreq>' + item.changefreq + '</changefreq>\n';
                    smContent += '  </url>\n'
                });
                smContent += '</urlset>';
                var sitemap_content = '<?xml version="1.0" encoding="UTF-8"?>\n' + smContent;
                fs.writeFileSync(save_path, sitemap_content);
                logger.info('sitemap生成成功。');
            } else {
                logger.warn('sitemap生成失败。');
                logger.info(err.message);
                logger.info(err.code);
            }
        });
    },
};

module.exports = reptile;





