/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016/4/5.
 * 支付宝，支付
 */
var config = require('../libs/config.js');
//var request = require('request');
var cryptoUtils = require('./cryptoUtils');
var httpsRestApiProxy = require("../libs/httpsRestApiProxy");
var authRestApiProxy = require("../libs/authRestApiProxy");
var payConstant =require('../libs/payConstant.js');
var log4js = require('../libs/log4jsUtil.js');
var logger = log4js.getLogger('alipayUtil');
var alipayUtil = {};
logger.debug(cryptoUtils.createPaySignature('g63x119zi1qauzeahmvw9ao7m2de3xut','_input_charset=utf-8&body=测试1&notify_url=http://商户网关地址/create_direct_pay_by_user-JAVA-UTF-8/notify_url.jsp&out_trade_no=aa2&partner=2088221246912513&payment_type=1&return_url=http://商户网关地址/create_direct_pay_by_user-JAVA-UTF-8/return_url.jsp&seller_email=<EMAIL>&service=create_direct_pay_by_user&show_url=https://mail.ronglian.com&subject=name1&total_fee=0.01'));

/**
 *组装支付宝请求对象
 * @param alipayInfo  支付宝信息
 * @param order 订单信息
 */
function alipayRequestObject(alipayInfo,order){
    var requestObject = {};
    requestObject.body = order.orderItemVos[0].goodsName;//TODO--订单描述
    //服务器异步通知页面路径,需http://格式的完整路径，不能加?id=123这类自定义参数
    requestObject.notify_url = config.AlipayService.notify_url;
    requestObject.out_trade_no = order.orderSn;//订单号
    requestObject.partner=alipayInfo.partner;//PID
    requestObject.payment_type = config.AlipayService.payment_type;
    //页面跳转同步通知页面路径,需http://格式的完整路径，不能加?id=123这类自定义参数，不能写成http://localhost/
    requestObject.return_url = config.AlipayService.return_url;

    requestObject.seller_email = alipayInfo.accountNum;//收款账号
    requestObject.service =  config.AlipayService.service;
    //requestObject._input_charset = config.AlipayService._input_charset;
    //requestObject.show_url =  config.AlipayService.show_url; //商品展示Url
    requestObject.subject = order.orderItemVos[0].goodsName;//订单名称
    requestObject.total_fee = order.orderPrice;//付款金额
    //requestObject.anti_phishing_key='';//防钓鱼时间戳
    //requestObject.exter_invoke_ip='';//客户端Ip地址
    return requestObject;
}

function orderedKeys(alipayObject){

}

/**
 * 对象转化参数
 * @param alipayObject
 */
function paramAlipayObject(alipayObject){
    //key排序
    var keys =   Object.keys(alipayObject).sort();
    logger.debug(JSON.stringify(keys));
    var alipayParam = '';
    for(var i=0;i< keys.length;i++){
        var prop = keys[i];
        if('sign'==prop||'sign_type'==prop){
            continue;
        }
        alipayParam=alipayParam+'&'+prop+'='+alipayObject[prop];
    }
   /* for(var prop in alipayObject){
        if('sign'==prop||'sign_type'==prop){
            continue;
        }
        alipayParam=alipayParam+'&'+prop+'='+alipayObject[prop];
    }*/
    return alipayParam;
}

/**
 * 查询商户的支付宝信息
 * @param callback
 */
alipayUtil.queryAlipayMgr = function (callback){
    var url = '/pay/q/'+payConstant.PAY_ALIPAY+'/'+config.RetailerService.retailersId;
    authRestApiProxy.get('RetailerService', url, function resultData(err, resultData) {
        if(err){
            logger.error("商家支付宝信息读取失败");
            callback(err, null);
        }else{
            if(resultData.status===1){//如果支付方式未启用
                callback(null, resultData);
            }else{
                var errObj = {};
                errObj.status='FAILURE';
                errObj.message='商家尚未启用支付宝，暂时无法完成支付';
                callback(errObj, null);
            }
        }
    });
}

/**
 * 支付宝支付操作
 * @param order 订单信息
 * see {
 *      partner:'',
 *      accountNum:'',
 *      secretKey:''
 *      }
 */
alipayUtil.payOrder = function payOrder(order,callback){

    alipayUtil.queryAlipayMgr(function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
            callback(err,null);
        }else{
            var secretKey = data.secretKey;//签名
            var alipayObject = alipayRequestObject(data,order);//
            //把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
            var content = '_input_charset=UTF-8'+paramAlipayObject(alipayObject);  //整合全部参数
            var mysign = cryptoUtils.createPaySignature(secretKey,content);
            var params =content+ '&sign='+mysign+'&sign_type=MD5';
            var url = encodeURI(config.AlipayService.url+params);
            callback(null,url);
        }

    });

}
/**
 * 验证支付宝返回状态，
 * 1、验证信息是否为支付宝发送
 * 2、验证商户签名
 * @param payParams 默认是排序过的，不需要重新排序
 * @param callback
 */
alipayUtil.verifyPayResp = function verifyPayResp(payParams,callback){

   alipayUtil.queryAlipayMgr(function(err,data) {
        if (err) {
            logger.error(JSON.stringify(err));
            callback(err, null);
        } else {
            //校验seller_id
           var seller_id = payParams.seller_id;//支付宝返回的商户ID
            var partner = data.partner;//合作者身份ID，商户的支付宝注册ID
            if(seller_id===partner){//验证合作者身份ID，商品的卖家
                var sign = payParams.sign;//返回的签名后的数据
                var content = paramAlipayObject(payParams);//验证参数
                content = content.substr(1,content.length);
                var secretKey = data.secretKey;//商户key
                var mysign = cryptoUtils.createPaySignature(secretKey,content);//签名
                if(sign===mysign){//签名一致
                    //进一步验证消息是否为支付宝发送
                    var notify_id = payParams.notify_id;
                    var url = config.AlipayService.verify_url  + "partner=" +  data.partner + "&notify_id=" + notify_id;
                    httpsRestApiProxy.get(url, function (vErr,vData){
                        if(vErr){
                            callback(vErr,null);
                        }else{
                            if('true'===vData){
                                callback(null,vData);
                            }else{
                                var err1 ={};
                                err1.message='请求来源不是支付宝';
                                callback(err1,null);
                            }
                        }
                    });
                }else{
                    var errData = {};
                    errData.message='签名错误';
                    callback(errData,null);
                }
            }else{
                var errData = {};
                errData.message='合作者身份ID不一致';
                callback(errData,null);
            }
        }
   });
}


module.exports = alipayUtil;