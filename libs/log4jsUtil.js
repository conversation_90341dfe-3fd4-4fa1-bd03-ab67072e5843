/**
 * Created by RONGLIAN on 2016/1/26.
 * Modified for Vue 3 migration testing
 */
var log4js = require('log4js');
var path = require('path');

// 使用新版本的 log4js API
log4js.configure({
    appenders: {
        console: { type: 'console' },
        file: {
            type: 'dateFile',
            filename: path.resolve(__dirname,'../logs/shopping'),
            pattern: "-yyyy-MM-dd.log",
            alwaysIncludePattern: true
        }
    },
    categories: {
        default: { appenders: ['console', 'file'], level: 'info' }
    }
});

module.exports = log4js;
