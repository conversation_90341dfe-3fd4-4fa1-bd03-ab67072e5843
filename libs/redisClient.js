var redisPool = require('./redisPool.js');
var configs = require('./config.js')

// 为测试 Vue 3 迁移，创建一个模拟的 Redis 客户端
var redisClient = {
    //向redis保存数据
    set: function (key, value, timeOut, callback) {
        console.log('模拟 Redis SET:', key, value);
        if (callback) callback(null, 'OK');
    },

    //从redis中获取数据
    get: function (key, callback) {
        console.log('模拟 Redis GET:', key);
        callback(null, null); // 始终返回 null，表示缓存未命中
    },

    //设置数据的timeout时间，超过这个时间，数据自动失效
    expire: function (key, timeout, callback) {
        console.log('模拟 Redis EXPIRE:', key, timeout);
        if (callback) callback(null, 'OK');
    },

    //删除
    remove: function (key, callback) {
        console.log('模拟 Redis REMOVE:', key);
        if (callback) callback(null);
    }
};

// 注释掉测试代码
// redisClient.remove('wsssssssssname', function (err) {
//     console.log('删除失败了');
// });

module.exports = redisClient;