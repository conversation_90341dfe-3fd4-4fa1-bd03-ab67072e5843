var crypto = require('crypto');
var BufferHelper = require('bufferhelper');
var cryptoutils = {};

//original参数类型为Buffer类型，返回值也是Buffer类型
function alignToLen(original , len){
	var bufKey = new Buffer(len);
  for(var i=0;i<len;i++){
    if(i<original.length){
       bufKey.writeInt8(original.readInt8(i),i);
    }else{
       bufKey.writeInt8(0,i);
    }
  }
  return bufKey;
}

function des3(key,content,fn){
	key = alignToLen(key,24);
	iv = new Buffer(0);
	var cipher =  fn('des-ede3',key,iv);
	cipher.setAutoPadding(true);
	var ciph = cipher.update(content);
	ciph = Buffer.concat([ciph,cipher.final()]);
	return ciph;
}

function md5(text){
	var md5 = crypto.createHash('md5');
	md5.update(text);
	return md5.digest('hex');
}

function aes(key,content,fn){
	key = alignToLen(key,16);
	iv = new Buffer(0);
	var cipher =  fn('aes-128-ecb',key,iv);
	cipher.setAutoPadding(true);
	var ciph = cipher.update(content);
	ciph = Buffer.concat([ciph,cipher.final()]);
	return ciph;
}
/**
 * 用3des进行加密
 */
cryptoutils.encryptUse3DES = function(key,content){
	return des3(key,content,crypto.createCipheriv);
}
/**
 * 用3des进行解密
 */
cryptoutils.decryptUse3DES = function(key,ciphertext){
	return des3(key,ciphertext,crypto.createDecipheriv);
}
/**
* 用AES加密
*/
cryptoutils.encryptUseAes = function(key,content){
	return aes(key,content,crypto.createCipheriv);
}
/**
 用AES解密
*/
cryptoutils.decryptUseAes = function(key,content){
	return aes(key,content,crypto.createDecipheriv);
}

/**
  计算数字签名
*/
cryptoutils.createSignature = function(key,content){
	var result = md5(key);
	if(content == null || content == undefined){
		content = "";
	}
    var buffer = new BufferHelper();
    buffer.concat(new Buffer(result));
    buffer.concat(new Buffer(content));
    result = md5(buffer.toBuffer().toString('binary'));
	return result;
}

/**
 计算数字签名,支付宝
 */
cryptoutils.createPaySignature = function(key,content){
	var buffer = new BufferHelper();
	buffer.concat(new Buffer(content));
	if(key!==null){
		buffer.concat(new Buffer(key));
	}
	return  md5(buffer.toBuffer().toString('binary'));
}

module.exports = cryptoutils;