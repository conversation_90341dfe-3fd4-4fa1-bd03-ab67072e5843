/**
 * Created by chend<PERSON>jie on 2016/3/30.
 */

var authRestApiProxy = require('./authRestApiProxy');
var log4js = require('./log4jsUtil.js');
var logger = log4js.getLogger('logUtil');
var config = require('./config');
var emailUtil={};

/**
 * 发邮件
 * @param emailVo
 * @param callback
 */
emailUtil.sendEmail= function(emailVo,callback){
    //var eemailVo ={
    //    receiver :'',
    //    subject:'',
    //    content:''
    //}
    emailVo.shopId=config.EmailService.retailersId;
    authRestApiProxy.post('EmailService','/emailMessage/a',emailVo,function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
        }
        if(callback!=undefined){
            callback(err,data);
        }
    });
};

module.exports = emailUtil;