/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016/4/12.
 */
var log4js = require('./log4jsUtil.js');
var redisClient = require("./redisClient");
var logger = log4js.getLogger('commonUtil');
var authRestApiProxy =  require('./authRestApiProxy.js');
var config =  require('./config.js');
var commonUtil = {};

/**
 *跳转到登录页面
 * @param req 必须
 * @param res 必须
 * @param data 头部和尾部的数据
 *
 */
commonUtil.renderToLoginPage = function (req,res, data) {

    data.title = "用户登录";
    var custEmail = req.cookies.custEmail;
    data.custEmail = custEmail == null ? "" : custEmail;
    res.render("customer/login", data);

}
/**
 *设置超时
 * @param res
 */
commonUtil.respSessionTimeOut = function (res) {
    var data = {};
    data.sessionTimeOut=true;
    var jsonStr = JSON.stringify(data);
    res.write(jsonStr);
    res.end();
}
/**
 * 加载全部省份
 * @param callback
 */
commonUtil.loadProvs = function(callback){
    var url = '/district/q?parentId='+config.DistrictService.superId;
    authRestApiProxy.get('DistrictService',url,function(err,data){
        callback(err,data);
    });
}

/**
 *加载支付方式
 * @param callback
 */
commonUtil.loadPayChannels = function(callback){
    var url = '/pay/q/'+config.RetailerService.retailersId;
    authRestApiProxy.get('RetailerService',url,function(err,data){
        if(err){
            callback(err,null);
        }else{
            var payChannels = {};
            for (i = 0; i < data.length; i++) {
                if (data[i].status == 1) {//如果是已经开启状态
                    if(data[i].payChannelName=='PAY_WECHAT'){//微信
                        payChannels.PAY_WECHAT=true;
                    }else if(data[i].payChannelName=='PAY_ALIPAY'){//支付宝
                        payChannels.PAY_ALIPAY=true;
                    }
                }
            }
            callback(null,payChannels);

        }
    });
}
//加载商品分类
commonUtil.loadGoodsCategories=function (callback){
    var systemsConfig = config['RetailerService'];
    var retailersId = systemsConfig['retailersId'];
    var key = 'retailers_goodsCategory_'+retailersId;
    redisClient.get(key,function(err1,data1){
        if(err1){
            callback(err1,null);
        }else if(data1){
            callback(null,JSON.parse(data1));
        }else{//redis没有取到值，调用restful接口从库里取最新数据
            commonUtil.setGoodsCategories(function(err,data){
                if(err){
                    logger.error(JSON.stringify(err));
                    callback(err,null);
                }else if(data){
                    callback(null,data);
                }
            });
        }
    });
}
//把商品分类放入redis
commonUtil.setGoodsCategories=function (callback){
    var systemsConfig = config['RetailerService'];
    var retailersId = systemsConfig['retailersId'];
    var key = 'retailers_goodsCategory_'+retailersId;
    authRestApiProxy.post('RetailerService','/good/category/qa',{},function(err,data){
        if(err){
            logger.error(JSON.stringify(err));
            if(callback!=undefined){
                callback(err,null);
            }
        }else  if(data){
            redisClient.set(key,JSON.stringify(data),null,function(redisErr,redisData){
                if(redisErr){
                    logger.error('redis exception happened when cacheActions: '+JSON.stringify(redisErr));
                    if(callback!=undefined) {
                        callback(redisErr,null);
                    }
                }else if(redisData!=null){
                    //logger.debug(redisData);
                    if(callback!=undefined){
                        callback(null,data);
                    }
                }
            });
        }
    });

}
/**
 * 对象合并(属性重复时保留obj2中的值)
 * @param obj1
 * @param obj2
 * <AUTHOR>
 */
commonUtil.mergeObject = function(obj1,obj2,callback){
    for(var element in obj2){
        obj1[element] = obj2[element];
    }
    callback(obj1);
};
module.exports = commonUtil;