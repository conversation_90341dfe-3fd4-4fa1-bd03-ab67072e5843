/**
 * Created by RONGLIAN on 2016/1/20.
 * Modified for Vue 3 migration testing
 */
// 临时注释掉实际的 API 调用，用于测试 Vue 3 迁移
// var restApiProxy = require('./restApiProxy');
// var querystring = require('querystring');
// var url = require('url');
// var cryptoUtils = require('./cryptoUtils');
var config = require('./config');

var authRestApiProxy = {};
/**
 *
 * @param systemName
 * @param option
 * @param callback 第一个参数为错误对象
 * id 1 请求后台服务超时
 * id 2 后台请求数据错误
 * id 3 请求后台服务错误
 * id 4  数字签名错误    --
 * id 5  未知错误    --
 */
// 临时注释掉实际的 API 调用，用于测试 Vue 3 迁移
// authRestApiProxy.sendRequest = function (systemName, option, callback) {
//     // 实现已注释掉
// }


// 为测试 Vue 3 迁移，返回模拟数据
authRestApiProxy.post = function (systemName, urlString, option, callback) {
    console.log('模拟 API 调用:', systemName, urlString, option);

    // 根据不同的 URL 返回不同的模拟数据
    if (urlString.includes('/merchant/q')) {
        // 商家信息
        callback(null, {
            retailersName: "测试商户",
            retailersLogo: "/logo.svg",
            retailersRecord: "2023;测试公司;京ICP备12345678号"
        });
    } else if (urlString.includes('/page/tabs/q')) {
        // 页面标签
        callback(null, [
            { tabsId: "1", tabsName: "首页", modulesUrl: "homepage" },
            { tabsId: "2", tabsName: "关于我们", modulesUrl: "about" },
            { tabsId: "3", tabsName: "产品服务", modulesUrl: "product" },
            { tabsId: "4", tabsName: "联系我们", modulesUrl: "contact" }
        ]);
    } else {
        // 默认返回空数据
        callback(null, {});
    }
}

// 为测试 Vue 3 迁移，返回模拟数据
authRestApiProxy.get = function (systemName, urlString, callback) {
    console.log('模拟 API 调用:', systemName, urlString);

    // 默认返回空数据
    callback(null, {});
}

module.exports = authRestApiProxy;