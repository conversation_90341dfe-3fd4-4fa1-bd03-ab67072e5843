/**
 * Created by <PERSON><PERSON><PERSON> on 2016-08-25 .
 */
var config = require('../libs/config.js')
var smsUtil = {};
TopClient = require('./topClient').TopClient;
var client = new TopClient({
    'appkey':config.smsService.appkey,
    'appsecret':config.smsService.appsecret,
    'REST_URL':'http://gw.api.taobao.com/router/rest'});

/**
 * 发送短信验证码
 * @param recNum 目标号码，字符串
 * @param data 验证码
 */
smsUtil.sendVerifyCode = function(recNum,data,callback){

    client.execute('alibaba.aliqin.fc.sms.num.send', {
        'sms_type':'normal',
        'sms_free_sign_name':config.smsService.sms_free_sign_name,//头部签名
        'sms_param':'{\"num\":\"' +data+ '\"}',
        'rec_num':recNum,//'18210554795'
        'sms_template_code':config.smsService.verify_template_code//'SMS_13285533'
    }, function(error, response) {
        if (!error){
            console.log(response);
            callback(null, response);
        }else {
            console.log(error);
            callback(error, null);
        }
    });
}


module.exports = smsUtil;