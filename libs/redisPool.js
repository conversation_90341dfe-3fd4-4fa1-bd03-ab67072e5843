/**
 * Created by RONGLIAN on 2016/1/21.
 * Modified for Vue 3 migration testing
 */

var config = require('./config.js');

// 为测试 Vue 3 迁移，创建一个模拟的 Redis 池
var redisPool = {
    acquire: function(callback) {
        // 创建一个模拟的 Redis 客户端
        var mockClient = {
            get: function(key, callback) {
                console.log('模拟 Redis GET:', key);
                callback(null, null); // 始终返回 null，表示缓存未命中
            },
            set: function(key, value, expireMode, expireTime, callback) {
                console.log('模拟 Redis SET:', key);
                if (callback) callback(null, 'OK');
            },
            end: function() {
                console.log('模拟 Redis 连接关闭');
            }
        };

        // 立即返回模拟客户端
        callback(null, mockClient);
    },
    release: function(client) {
        // 什么都不做
        console.log('模拟 Redis 连接释放');
    }
};

module.exports = redisPool;