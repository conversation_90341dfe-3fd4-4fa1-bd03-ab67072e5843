var urllib = require('urllib');
var util = require('./topUtil');

/**
 * TOP API Client.
 * Modified for Vue 3 migration testing
 *
 * @param {Object} options, must set `appkey` and `appsecret`.
 * @constructor
 */

function TopClient(options) {
    if (!(this instanceof TopClient)) {
        return new TopClient(options);
    }
    options = options || {};
    if (!options.appkey || !options.appsecret) {
        throw new Error('appkey or appsecret need!');
    }
    this.REST_URL = options.REST_URL || 'http://gw.api.taobao.com/router/rest';
    this.appkey = options.appkey;
    this.appsecret = options.appsecret;
}

/**
 * Invoke an api by method name.
 *
 * @param {String} method, method name
 * @param {Object} params
 * @param {Array} reponseNames, e.g. ['tmall_selected_items_search_response', 'tem_list', 'selected_item']
 * @param {Object} defaultResponse
 * @param {String} type
 * @param {Function(err, response)} callback
 */
TopClient.prototype.invoke = function (method, params, reponseNames, defaultResponse, type, callback) {
    console.log('模拟 TOP API 调用:', method, params);

    // 返回模拟数据
    var mockResult = {};
    if (method.includes('alibaba.aliqin.fc.sms.num.send')) {
        mockResult = {
            alibaba_aliqin_fc_sms_num_send_response: {
                result: {
                    success: true,
                    err_code: '0',
                    model: '123456'
                }
            }
        };
    } else {
        // 默认响应
        var responseName = util.getApiResponseName(method);
        mockResult[responseName] = {
            success: true
        };
    }

    // 处理响应名称
    var response = mockResult;
    if (reponseNames && reponseNames.length > 0) {
        for (var i = 0; i < reponseNames.length; i++) {
            var name = reponseNames[i];
            response = response[name];
            if (response === undefined) {
                break;
            }
        }
    }
    if (response === undefined) {
        response = defaultResponse;
    }

    // 异步返回结果
    setTimeout(function() {
        callback(null, response);
    }, 100);
};

TopClient.prototype._wrapJSON = function (s) {
    var matchs = s.match(/\"id\"\:\s?\d{16,}/g);
    if (matchs) {
        for (var i = 0; i < matchs.length; i++) {
            var m = matchs[i];
            s = s.replace(m, '"id":"' + m.split(':')[1].trim() + '"');
        }
    }
    return s;
};

var IGNORE_ERROR_CODES = {
    'isv.user-not-exist:invalid-nick': 1
};

/**
 * Request API.
 *
 * @param {Object} params
 * @param {String} [type='GET']
 * @param {Function(err, result)} callback
 * @public
 */
TopClient.prototype.request = function (params, type, callback) {
    console.log('模拟 TOP API 请求:', params);

    // 直接返回模拟数据
    var mockResult = {};
    if (params.method.includes('alibaba.aliqin.fc.sms.num.send')) {
        mockResult = {
            alibaba_aliqin_fc_sms_num_send_response: {
                result: {
                    success: true,
                    err_code: '0',
                    model: '123456'
                }
            }
        };
    } else {
        // 默认响应
        var responseName = util.getApiResponseName(params.method);
        mockResult[responseName] = {
            success: true
        };
    }

    // 异步返回结果
    setTimeout(function() {
        callback(null, mockResult);
    }, 100);
};

/**
 * Get now timestamp with 'yyyy-MM-dd HH:mm:ss' format.
 * @return {String}
 */
TopClient.prototype.timestamp = function () {
    return util.YYYYMMDDHHmmss();
};

/**
 * Sign API request.
 * see http://open.taobao.com/doc/detail.htm?id=111#s6
 *
 * @param  {Object} params
 * @return {String} sign string
 */
TopClient.prototype.sign = function (params) {
    var sorted = Object.keys(params).sort();
    var basestring = this.appsecret;
    for (var i = 0, l = sorted.length; i < l; i++) {
        var k = sorted[i];
        basestring += k + params[k];
    }
    basestring += this.appsecret;
    return util.md5(basestring).toUpperCase();
};

/**
 * execute top api
 */
TopClient.prototype.execute = function (apiname, params, callback) {
    this.invoke(apiname, params, [util.getApiResponseName(apiname)], null, 'POST', callback);
};

exports.TopClient = TopClient;
