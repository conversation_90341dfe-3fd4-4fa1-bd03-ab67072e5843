// 临时注释掉 Redis 客户端，用于测试 Vue 3 迁移
// var redisClient = require("../libs/redisClient");
var constants = require("../libs/constants.js");
var authRestApiProxy = require("../libs/authRestApiProxy")
var log4js = require('../libs/log4jsUtil.js')
var config = require('../libs/config.js')
var logger = log4js.getLogger('merchantUtil');
var imgDomain = config.ImageService.domain;
var merchantUtil = {};


/**
 * 获取商家商家信息
 * @param callback
 */
merchantUtil.getMerchantInfo = function (callback) {
    authRestApiProxy.post('RetailerService', "/merchant/q", {}, function resultData(err, resultData) {
        if (resultData != null && err == null) {
            callback(null, resultData);
        } else {
            logger.info("商家读取失败")
            callback(err, null);
        }
    });
};

/**
 * 获取商户的全部信息（头部，底部，商户信息）
 * @param callback
 */
merchantUtil.getMerchantAllInfo = function (req, callback) {
    // 为测试 Vue 3 迁移，返回模拟数据
    var mockData = {
        merchant: {
            retailersName: "测试商户",
            retailersLogo: "/logo.svg",
            retailersRecord: "2023;测试公司;京ICP备12345678号"
        },
        customer: req.session.customer,
        imgDomain: imgDomain,
        pageTabs: [
            { tabsId: "1", tabsName: "首页", modulesUrl: "homepage" },
            { tabsId: "2", tabsName: "关于我们", modulesUrl: "about" },
            { tabsId: "3", tabsName: "产品服务", modulesUrl: "product" },
            { tabsId: "4", tabsName: "联系我们", modulesUrl: "contact" }
        ],
        ptData: [
            { tabsId: "1", tabsName: "首页", modulesUrl: "homepage" },
            { tabsId: "2", tabsName: "关于我们", modulesUrl: "about" },
            { tabsId: "3", tabsName: "产品服务", modulesUrl: "product" },
            { tabsId: "4", tabsName: "联系我们", modulesUrl: "contact" }
        ]
    };

    // 处理商户记录信息
    if (mockData.merchant.retailersRecord.indexOf(";") > 0) {
        var recordArray = mockData.merchant.retailersRecord.split(';');
        mockData.merchant.footdate = recordArray[0];
        mockData.merchant.footname = recordArray[1];
        mockData.merchant.footRecord = recordArray[2];
    }

    callback(null, mockData);
};
/**
 * 获取商家栏目
 * @param callback
 */
merchantUtil.getPageTabsInfo = function (callback) {
    authRestApiProxy.post('RetailerService', "/page/tabs/q", {'parentId':'0','isshow':'1'}, function resultData(err, resultData) {
        if (resultData != null && err == null) {
            callback(null, resultData);
        } else {
            logger.info("商家栏目读取失败")
            callback(err, null);
        }
    });
};

/**
 * 获取商家底部栏目
 * @param callback
 */
merchantUtil.getBottomTabsInfo = function (callback) {
    authRestApiProxy.post('RetailerService', "/bottom/tabs/q", {}, function resultData(err, resultData) {
        if (resultData != null && err == null) {
            callback(null, resultData);
        } else {
            logger.info("商家底部栏目读取失败")
            callback(err, null);
        }
    });
};

module.exports = merchantUtil;