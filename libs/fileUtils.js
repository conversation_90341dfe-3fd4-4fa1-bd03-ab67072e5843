/**
 * Created by gao on 2016/1/21.
 */
formidable = require("formidable"); //载入 formidable
var sizeOf = require('image-size');
fileUtils = {};

/**
 * 将上传文件解析为base64串
 * @param req 请求req
 * @param option 选项
 *  {
 *      onEnd:function(data){
 *          data为解析后的文件数据，结构如下：
 *          data{
 *              fields{字段1:字段1值,字段2:字段2值, ......},
 *              files[
 *                  {
 *                      fileName: 文件名
 *                      fileSize: 文件大小
 *                      data: 文件数据(base64编码)
 *                  }
 *              ]
 *          }
 *      },
 *      onError: function(err){
 *      }
 *  }
 *
 */
fileUtils.parseFilesBase64 = function(req,option){
    var fields = {};
    var fileTrunks = new Array();
    var files = new Array();
    var form = new formidable.IncomingForm();
    if(option.onError){
        form.on('error', function(err){
            option.onError(err);
        });
    }
    //传输结束
    form.on('end', function(){
        //组装文件
        var length = fileTrunks.length;
        for( var i = 0; i < length; i ++ ){
            var file = {};
            file.fileName = fileTrunks[i].fileName;
            file.fileSize = fileTrunks[i].fileSize;
            file.contentType = fileTrunks[i].contentType.replace("/pjpeg","/jpeg").replace("/x-png","/png");
            var fileBuffer= Buffer.concat( fileTrunks[i].trunks, file.fileSize);
            //获取图片大小
            if(file.contentType.indexOf("mage/")>0) {
                if(file.contentType.indexOf("ico")>0)
                {
                    file.width = 99999
                    file.height = 99999;
                    file.type="ico";
                }
                else {
                    var imageInfo = sizeOf(fileBuffer);
                    file.width = imageInfo.width;
                    file.height = imageInfo.height;
                    file.type = imageInfo.type;
                }
            }
            file.data =fileBuffer.toString('base64');
            files.push(file);
        }

        var data = {};
        data.fields = fields;
        data.files = files;

        if(option.onEnd) {
            option.onEnd(data);
        }
    });
    form.onPart = function(part) {
        if(! part.filename){//解析字段
            part.addListener('data', function(data) {
                fields[part.filename] = data;
            });
        }else{//接收文件数据
            part.addListener('data', function(data) {
                var fileTrunk = null;
                var length = fileTrunks.length;
                for( var i = 0; i < length; i ++ ) {
                    if (fileTrunks[i].fileName == part.filename) {
                        fileTrunk = fileTrunks[i];
                        break;
                    }
                }
                if(fileTrunk == null){//新文件
                    var fileTrunk = {};
                    fileTrunk.fileName = part.filename;
                    fileTrunk.fileSize = 0;
                    fileTrunk.contentType=part.mime;
                    fileTrunk.trunks = new Array();
                    fileTrunks.push(fileTrunk);
                }
                //保存文件数据
                fileTrunk.trunks.push(data);
                fileTrunk.fileSize += data.length;
            });
        }
    }
    form.parse(req); //解析request对象
}

module.exports = fileUtils;