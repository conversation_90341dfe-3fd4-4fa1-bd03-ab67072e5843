/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016-09-22 .
 */
/**
 * Created by rong<PERSON> on 2016-08-25 .
 */
var config = require('../libs/config.js');
var redisClient = require('../libs/redisClient.js');
var authRestApiProxy = require('../libs/authRestApiProxy.js');
var constants = require('../libs/constants.js');
var cookieUtil = require('../libs/cookieUtil.js');
var loginUtil = {};

/**
 * 发送短信验证码
 * @param recNum 目标号码，字符串
 * @param data 验证码
 */
loginUtil.executeLogin = function(req,callback){
    var key = req.body.custMobile;
    redisClient.get(key,function(err,data){
        if (data != null && err == null) {
            if(req.body.moveCode == data){
                req.body.loginIp = getClientIp(req);
                authRestApiProxy.post('RetailerService', "/customer/q/lg", req.body, function resultData(err, data) {
                    if (data != null && err == null) {
                        req.session.customer = data;
                        if (req.body.rememberMobile == 'true') {
                            cookieUtil.set(res, "custMobile", req.body.custMobile, constants.LOGIN_COOKER_TIMEOUT);
                        }
                        redisClient.remove(req.body.custMobile);
                        callback(null,{result: constants.CONST_SUCCESS });//登录成功
                    } else {
                        callback(err,null);//登录失败
                    }
                });
            }else{
                callback({'pointLanguage':'0'},null);;//标识动态码输入错误，         纠正：redis客户端错误，属于系统错误
            }
        }else{
            callback({'pointLanguage':'1'},null);//标识动态码已失效  纠正：验证码错误或者失效
        }
    });

}
//获取客户ip
function getClientIp(req) {
    return req.headers['x-forwarded-for'] ||
        req.connection.remoteAddress ||
        req.socket.remoteAddress ||
        req.connection.socket.remoteAddress;
};

module.exports = loginUtil;