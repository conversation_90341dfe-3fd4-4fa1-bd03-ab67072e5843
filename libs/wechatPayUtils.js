/**
 * Created by ch<PERSON><PERSON><PERSON><PERSON> on 2016-06-20 .
 */

var config = require('../libs/config.js');
var httpsRestApiProxy = require("../libs/httpsRestApiProxy");
var log4js = require('../libs/log4jsUtil.js');
var constants = require('../libs/constants.js');
var payConstant =require('../libs/payConstant.js');
var cryptoUtils = require('./cryptoUtils');
var authRestApiProxy = require("../libs/authRestApiProxy");
var logger = log4js.getLogger('weixinUserUtil');
var xml2js = require('xml2js');
var os = require('os');
var wechatPayUtils = {};

var chars = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'];
function generateMixed(n) {
    var res = "";
    for(var i = 0; i < n ; i ++) {
        var id = Math.ceil(Math.random()*35);
        res += chars[id];
    }
    return res;
}


/**
 *组装微信请求对象
 * @param wechatInfo  支付宝信息
 * @param order 订单信息
 */
function wechatRequestObject(wechatInfo,order){
    var requestObject = {};
    requestObject.mch_id =wechatInfo.accountNum; //'**********';//商家号
    requestObject.appid=wechatInfo.partner;//'wxbaaaf28e79d71ad5';//公众号ID
    requestObject.nonce_str=generateMixed(12);//生成随机数
    requestObject.spbill_create_ip=getIPAdress();//终端IP
    requestObject.trade_type='NATIVE';//交易类型，NATIVE 原生扫码支付
    requestObject.notify_url=config.WeChatService.notify_url;//通知地址,外网可直接访问
    requestObject.body=order.orderItemVos[0].goodsName;//商品描述,简介
    //payInfo.detail='测试微信支付';//商品名称明细列表,详情
    requestObject.out_trade_no= order.orderSn;;//商户订单号
    requestObject.total_fee=order.orderPrice*100;//总金额 ，微信支付以分为单位
    requestObject.product_id=order.orderItemVos[0].goodsSn;//NATIVE 原生扫码支付 ,必填
    return requestObject;
}


/**
 * 对象转化参数
 * @param alipayObject
 */
function paramWechatObject(alipayObject){
    //key排序
    var keys =   Object.keys(alipayObject).sort();
    logger.debug(JSON.stringify(keys));
    var alipayParam = '';
    for(var i=0;i< keys.length;i++){
        var prop = keys[i];
        if('sign'==prop){
            continue;
        }
        alipayParam=alipayParam+'&'+prop+'='+alipayObject[prop];
    }
    return alipayParam.substr(1,alipayParam.length);
}

/**
 * 获取IP地址
 * @returns {*}
 */
function getIPAdress() {
    var interfaces = os.networkInterfaces();
    for (var devName in interfaces) {
        var iface = interfaces[devName];
        for (var i = 0; i < iface.length; i++) {
            var alias = iface[i];
            if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
                return alias.address;
            }
        }
    }
}

/**
 * 查询商户的微信信息
 * @param callback
 */
wechatPayUtils.queryWeChatMgr = function (callback){
    var url = '/pay/q/'+payConstant.PAY_WECHAT+'/'+config.RetailerService.retailersId;
    authRestApiProxy.get('RetailerService', url, function resultData(err, resultData) {
        if(err){
            logger.error("商家微信信息读取失败");
            callback(err, null);
        }else{
            if(resultData.status===1){//如果支付方式未启用
                callback(null, resultData);
            }else{
                var errObj = {};
                errObj.status='FAILURE';
                errObj.message='商家尚未启用微信支付，暂时无法完成支付';
                callback(errObj, null);
            }
        }
    });
}

/**
 *微信预支付
 * @param payInfo 交易数据对象
 * @param callback
 */
wechatPayUtils.prePay = function prePay(order, callback){
    wechatPayUtils.queryWeChatMgr(function(err, data) {
        if (err) {
            logger.error(JSON.stringify(err));
            callback(err, null);
        } else {
            var secretKey = data.secretKey;//签名  'ronglianbioservice2016shengwuyun';
            var payInfo = wechatRequestObject(data,order);

            //签名
            var content =paramWechatObject(payInfo);  //把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
            content = content+'&key='+secretKey;
            var mysign = cryptoUtils.createPaySignature(null,content).toUpperCase();
            payInfo.sign = mysign;
            //对象转化为xml
            wechatPayUtils.buildObject2Xml(payInfo,function(xml){
                httpsRestApiProxy.post(config.WeChatService.prePayUrl,xml, function (err1, data1) {
                    if (err1) {
                        logger.error(JSON.stringify(err1));
                        callback(err1, null);
                    } else {
                        wechatPayUtils.parseString(data1 ,function (result) {
                            //验证预支付接口返回结果是否是微信发过来的，是否被篡改
                            wechatPayUtils.vaildPrePayBack(secretKey,result,function (validData){
                                if(validData){
                                    callback(null, result);
                                }else{
                                    var errObj = {};
                                    errObj.status='FAILURE';
                                    errObj.message='预支付接口返回结果签名不正确，信息被篡改';
                                    callback(errObj, null);
                                }
                            });

                        });
                    }
                });
            });


        }
    });
}
/**
 *校验返回的数据签名，防止数据被篡改
 * @param secretKey 秘钥
 * @param payBackData 返回的对象（xml转化后得到）
 * @param callback 回调
 */
wechatPayUtils.vaildPrePayBack = function vaildPrePayBack(secretKey,payBackData,callback){
        var content = paramWechatObject(payBackData);
        content = content+'&key='+secretKey;
        var mysign = cryptoUtils.createPaySignature(null,content).toUpperCase();
        logger.info('结果签名：'+mysign);
        var vaildChecked = (mysign===payBackData.sign);//判断签名书否正确
        logger.info('签名比对结果：'+vaildChecked);
        callback(vaildChecked);
}

/**
 * 对象转化为xml，满足微信的要求
 * @param obj
 * @param callback 回调
 */
wechatPayUtils.buildObject2Xml = function buildObject2Xml(obj,callback){
    var builder =  new xml2js.Builder();  // JSON->xml
    builder.options.rootName='xml';
    builder.options.headless=true;
    var xml =  builder.buildObject(obj);
    logger.info('Json to xml ：'+xml);
    callback(xml);
}

/**
 *xml转化为对象，适用于微信支付的格式
 * @param xml xml文本
 * @param callback 回调
 */
wechatPayUtils.parseString = function parseString(xml,callback){
    var parser =  xml2js.Parser();//xml - json
    logger.debug('返回：' + xml);
    parser.options.trim=true;
    parser.options.explicitRoot=false;
    parser.options.explicitArray=false;
    parser.parseString(xml ,function (err, result) {
        if(err){
            logger.error('返回：' + JSON.stringify(err));
        }
        logger.debug('返回：' + result);
        logger.debug('返回：' + JSON.stringify(result));
        callback(result);
    });
}
/**
 * 处理并验证签名
 * @param xml
 * @param callback 返回对象数据
 */
wechatPayUtils.validNotifyBack = function validNotifyBack(xml,callback){
    //xml参数转化为对象
    wechatPayUtils.parseString(xml ,function (result) {
        if(result.return_code==='FAIL'){//处理接口调用错误
            logger.error(result.return_msg);
            callback(result,null);

        }else{
            if(result.result_code==='FAIL'){//处理业务失败
                logger.error(result.err_code_des);
                callback(result,null);
            }else{
                //拿到商户秘钥
                wechatPayUtils.queryWeChatMgr(function(wechatErr,wechatData){
                    if(wechatErr){
                        callback(wechatErr,null);
                    }else{
                        //验证签名，判断信息是否被篡改
                        wechatPayUtils.vaildPrePayBack(wechatData.secretKey,result,function(vaildChecked){
                            if(vaildChecked){//验证通过，数据有效
                                callback(null,result);
                            }else{
                                var errObj = {};
                                errObj.status='FAILURE';
                                errObj.message='微信支付回调信息签名不正确，信息被篡改';
                                callback(errObj,null);
                            }
                        });
                    }

                });

            }


        }


    });
}
module.exports = wechatPayUtils;