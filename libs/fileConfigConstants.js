/**
 * 权限图片尺寸和大小常量
 */
var fileConfigConstants = {
    /**
     * 通用图片上传文件大小(单位M)
     * rule(LEQ:小于等于,EQ:等于, GTE:大于等于)
     * @type {number}
     */
    CommonConfig: {
        size: 5 * 1024 * 1024,
        width: 3000,
        height: 3000,
        rule: 'LEQ',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度3000px*高度3000px'
    },
    //图像
    LogoConfig: {
        size: 5 * 1024 * 1024,
        width: 3000,
        height: 3000,
        rule: 'LEQ',
        type: 'gif,png,jpg,jpeg',
        prompt: '最小图片尺寸：宽度3000px*高度3000px'
    },

};
fileConfigConstants.IMG_FORMAT_ERROR='上传文件类型错误';
fileConfigConstants.IMG_SIZE_ERROR='上传文件大小应该小于5M';
fileConfigConstants.IMG_CONFIG_ERROR='上传文件配置错误';
module.exports = fileConfigConstants;


