var log4js = require('../libs/log4jsUtil.js');
var logger = log4js.getLogger('WechatUserUtils');
var https = require("https");
var configs = require('./config.js');
var wechatDevScreet = configs.wechatDevScreet;
var wechatUtils = {};
wechatUtils.getWechatUser = function (type, paramCode, res, url, menuData, req) {
    logger.info("code：" + paramCode);
    logger.info("type：" + type);
    if (paramCode === undefined || paramCode === '' || paramCode == null) {
        logger.error("获取企业微信code错误", paramCode);
        res.render('error');
    }
    //1.获取秘钥和企业id
    var corpid = '';
    var corpsecret = '';
    var agentid = "";
    //2.获取accesstoken
    wechatDevScreet.forEach(function (item) {
        logger.info('item.type:', item.type);
        logger.info('type:', type);
        if (item.type === type) {
            corpid = item.corpid;
            corpsecret = item.corpsecret;
            agentid = item.agentid;
        }
    });
    if (corpid === '' || corpsecret === '') {
        logger.error("企业微信配置错误corpsecret", corpsecret);
        logger.error("企业微信配置错误corpid", corpid);
        res.render('error');
        return;
    }
    var userid = req.cookies.userid;
    if (userid) {
        logger.info("从cookie 获取用户", userid);
        menuData.userid = userid;
        res.render(url, menuData);
        return;
    } else {
        https.get("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpid + "&corpsecret=" + corpsecret, (res2) => {
            res2.headers['content-type'] = 'text/xml;charset=utf-8';
            logger.info('accessTokenHeaders:', res2.headers);
            logger.info('accessTokenStatusCode:', res2.statusCode);
            res2.on('data', (d) => {
                logger.info(JSON.parse(d));
                var parse = JSON.parse(d);
                var errmsg = parse.errmsg;
                if (errmsg === 'ok') {
                    var accessToken = parse.access_token;
                    logger.info("accessToken", accessToken);
                    //通过token和code 获取用户信息
                    getUserInfo(accessToken, paramCode, agentid,res,res2,url, menuData);
                } else {
                    res.render('error');
                    logger.error("获取token失败" + parse);
                }
            });

        }).on('error', (e) => {
            res.render('error');
            logger.error(e);
        });
    }

};

function getUserInfo(acces_token, paramCode, agentid,res,res2, url, menuData) {
    https.get("https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=" + acces_token + "&code=" + paramCode + "&agentid=" + agentid, function (res1) {
        logger.info('userInfoheaders:', res1.headers);
        logger.info('userInfostatusCode:', res1.statusCode);
        res1.headers['content-type'] = 'text/xml;charset=utf-8';
        res1.on('data', function (data) {
            var parse = JSON.parse(data);
            logger.info('userInfoData', parse);
            var errmsg = parse.errmsg;
            if (errmsg === 'ok') {
                var userId = parse.UserId;
                logger.info("userId", userId);
                menuData.userid = userId;
                res.cookie('userid', userId, {maxAge:3600000});
                res.render(url, menuData);
            } else {
                res.render('error');
            }
        });
    }).on('error', (e) => {
        logger.error('userInfoErro', e);
        res.render('error');
    });
}

module.exports = wechatUtils;




