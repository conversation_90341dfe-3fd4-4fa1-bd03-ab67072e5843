/**
 * Created by RONGLIAN on 2016/1/20.
 */
var http = require("http");
var util = require("util");
var url = require("url");
var iconLite = require('iconv-lite')
var restApiProxy = {};


/**
 *
 * @param res
 * @param dataType json text  如果为空，则根据返回值的类型头信息确定
 * @param callback
 */
function requestHandller(res, option, callback) {
    var dataType = option['dataType'];
    if (!dataType) {
        var contentType = res.headers["content-type"];
        if (!contentType) {
            dataType = 'text';
        } else {
            contentType = contentType.split(':')[0];
            if (contentType === 'application/json') {
                dataType = 'json';
            } else {
                dataType = 'text';
            }
        }
    }
    var body = "";
    var chunks = [];
    res.on('data', function (chunk) {
        //body += chunk;
        chunks.push(chunk);
    }).on('end', function () {
        body = iconLite.decode(Buffer.concat(chunks), option['code']);
        if (dataType === 'text') {
            callback(null, body);
        } else {
            var data = JSON.parse(body);
            callback(null, data);
        }
    }).on('error', function () {
        var error = new Error('后台请求错误', 2);
        callback(error);
    });
}
/**
 *
 * @param option 属性
 * {
 *  protocol:
 *  host
 *  port
 *  path
 *  method:GET or POST
 *
 *  timeout:  单位s
 *  data：二进制类型
 *  dataType： json 或 text
 *  headers
 * }
 * @param callback 回调，第一个参数为错误对象，第二个为返回的数据
 */
restApiProxy.sendRequest = function (option, callback) {
    var requestOptions = {};
    if (option['protocol']) {
        requestOptions['protocol'] = option['protocol'];
    }
    if (option['host']) {
        requestOptions['host'] = option['host'];
    }
    if (option['path']) {
        requestOptions['path'] = option['path'];
    }
    if (option['port']) {
        requestOptions['port'] = option['port'];
    }
    if (option['method']) {
        requestOptions['method'] = option['method'];
    }
    if (option['headers']) {
        requestOptions['headers'] = option['headers'];
    } else {
        requestOptions['headers'] = {};
    }
    var req = http.request(requestOptions, function (res) {
        requestHandller(res, option, callback);
        // if (option['dataType']) {
        //     requestHandller(res, option, callback);
        // } else {
        //     requestHandller(res, null, callback);
        // }

    });
    req.on('error', function (e) {
        var error = new Error('请求后台服务错误', 3);
        console.log(e);

        console.log(e.message);
        callback(error);
    });

    /**
     * 设置超时时间
     */
    if (option['timeout']) {
        req.setTimeout(option['timeout'], function () {
            var error = new Error('请求后台服务超时', 1);
            callback(error);
            req.abort();
        });
    }

    var data = option['data'];
    if (data) {
        req.write(data);
    }
    req.end();

}
restApiProxy.get = function (urlString, callback, code) {
    //if((typeof url === 'string') || (url instanceof String)){
    var option = url.parse(urlString, null, false);
    console.log(option['host']);
    if (option['host']) {
        option['host'] = option['host'].split(':')[0];
    }
    if (option['hostname']) {
        option['hostname'] = option['hostname'].split(':')[0];
    }
    if (!code) {
        code = 'utf-8';
    }
    option["code"] = code;
    restApiProxy.sendRequest(option, callback);

}

restApiProxy.post = function (urlString, option, callback, code) {
    var allOptions = url.parse(urlString);
    if (allOptions['host']) {
        allOptions['host'] = allOptions['host'].split(':')[0];
    }
    if (allOptions['hostname']) {
        allOptions['hostname'] = allOptions['hostname'].split(':')[0];
    }
    if (!code) {
        code = 'utf-8';
    }
    option["code"] = code;
    for (var item in option) {
        allOptions[item] = option[item];
    }
    restApiProxy.sendRequest(allOptions, callback);
}

// restApiProxy.get("http://hq.sinajs.cn/list=sz002642", function (err, date) {
//
//     if (err) {
//         console.info(err)
//     }
//     else {
//         console.info(date);
//     }
//
// },"gb2312")
module.exports = restApiProxy;