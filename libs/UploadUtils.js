/**
 * Created by RONGLIAN on 2016/1/26.
 * Modified for Vue 3 migration testing
 */

var events = require("events");
var util = require("util");
var formidable = require('formidable');
// 临时注释掉 MongoDB 相关代码，用于测试 Vue 3 迁移
// var mongodb = require('mongodb'); // 2.0.31
var stream = require('stream');
var fs = require('fs');
// var Grid = require('gridfs-stream');
var URL = require('url');
var log4js = require('./log4jsUtil.js');
var config = require('./config.js');
var logger = log4js.getLogger('uploadUtil');
var path = require('path');


function UploadUtils() {
    //进度需要进行事件监听
    events.EventEmitter.call(this);

    UploadUtilsEvent = this;

    /**
     *
     * @param req 请求对象
     * @param callback 回调
     * @param callback.err 错误对象
     * @param callback.files 保存mongodb后的文件对象数组
     * @param callback.fields form表单字段
     */
    this.upload = function (req, callback) {
        var form = new formidable.IncomingForm();
        var uploadOption = {encoding: 'utf-8'};

        for (var key in uploadOption) {
            form[key] = uploadOption[key];
        }

        // 确保上传目录存在
        var uploadDir = path.join(__dirname, '../uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        form.uploadDir = uploadDir;

        var allFiles = [];
        var allField = {};
        var uploadError = false;
        var myerr = null;
        var myProgress = 0;
        var totalFiles = 0;
        var savedFiles = 0;
        var requestEnded = false;

        var asyncProgress = function (value) {
            if (myProgress === value) {
                return;
            }
            myProgress = value;
            UploadUtilsEvent.emit('progress', value);
        }

        function requestEnd() {
            if (requestEnded) {
                if (totalFiles === savedFiles) {
                    callback(myerr, allFiles, allField);
                }
            }
        }

        form
            .on('error', function (err) {
                callback(err);
            })
            .on('progress', function (bytesReceived, bytesExpected) {
                if (bytesReceived === 0) {
                    asyncProgress(0)
                } else {
                    var progress = bytesReceived / bytesExpected;
                    asyncProgress(progress);
                }
            })
            .on('field', function (name, value) {
                allField[name] = value;
            })
            .on('file', function (name, file) {
                //限制file大小(40M * 1024 * 1024 = 41943040 bytes)
                if (file.size > 41943040) {
                    logger.error('文件超限，最大40M,上传文件大小：' + file.size);
                    callback('上传文件超过40M限制');
                    return;
                }
                if (uploadError) {
                    console.log('错误了')
                    return;
                }

                totalFiles++;

                // 为文件生成唯一ID
                var fileId = Date.now() + '_' + Math.random().toString(36).substring(2, 15);
                var newFilePath = path.join(uploadDir, fileId + '_' + file.name);

                // 将临时文件移动到新位置
                fs.rename(file.path, newFilePath, function(err) {
                    if (err) {
                        uploadError = true;
                        myerr = err;
                        return;
                    }

                    logger.debug('文件已保存: ' + newFilePath);
                    allFiles.push({
                        fileId: fileId,
                        filename: file.name,
                        path: newFilePath
                    });

                    savedFiles++;
                    requestEnd();
                });
            })
            .on('end', function () {
                requestEnded = true;
            });

        form.parse(req, function (err, fields, files) {
            if (err) {
                callback(err);
            }
        });
    }

    /**
     * 根据对象Id获取对象
     *
     * @param objectId
     * @callback
     */
    this.getFileById = function (objectId, callback) {
        // 查找上传目录中的文件
        var uploadDir = path.join(__dirname, '../uploads');

        // 读取上传目录中的所有文件
        fs.readdir(uploadDir, function(err, files) {
            if (err) {
                callback(err, null);
                return;
            }

            // 查找以 objectId 开头的文件
            var matchingFile = files.find(function(file) {
                return file.startsWith(objectId + '_');
            });

            if (!matchingFile) {
                callback(new Error('File not found with ID: ' + objectId), null);
                return;
            }

            var filePath = path.join(uploadDir, matchingFile);

            try {
                // 创建文件读取流
                var readstream = fs.createReadStream(filePath);
                // 提取文件名（去掉 ID_ 前缀）
                var filename = matchingFile.substring(matchingFile.indexOf('_') + 1);

                // 创建一个模拟的 db 对象，以便与原代码兼容
                var mockDb = {
                    close: function() {
                        // 什么都不做
                        console.log('模拟关闭数据库连接');
                    }
                };

                callback(null, readstream, mockDb);
            } catch (err) {
                callback(err, null);
            }
        });
    }
}

util.inherits(UploadUtils, events.EventEmitter);

module.exports = UploadUtils;