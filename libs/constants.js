/**
 * 常量
 */
var retailersConstants = {};

//商家所有信息缓存（栏目，底部，商家信息）
retailersConstants.MERCHANTALLINFO="retailers_merchantAllInfo_";

//商家缓存
retailersConstants.MERCHANTINFO="retailers_merchantInfo_";

//商户栏目缓存
retailersConstants.MERCHANTPAGETABSINFO="retailers_merchantPageTabsInfo_";

retailersConstants.MERCHANTBOTTOMTABSINFO="retailers_merchantBottomTabsInfo_";

//栏目相关内容类型（组件URL）
retailersConstants.PAGE_TABS_TYPE_NEWS = 'news/index';
retailersConstants.PAGE_TABS_TYPE_GOODS = 'goods/index';
retailersConstants.PAGE_TABS_TYPE_CONTENT = 'content/index';

//文件下载路径
retailersConstants.FILE_DOWNLOAD_PATH="/file/download/";

//密码找回失效时间
retailersConstants.RETRIEVE_PWD_TIMEOUT=24 * 60 * 60;
//登陆cooker失效时间
retailersConstants.LOGIN_COOKER_TIMEOUT=2592000;

//样品状态失效时间
retailersConstants.SAMPLINGSTATE_COOKER_TIMEOUT=2592000;

//短信验证码失效时间
retailersConstants.SMSCODE_COOKER_TIMEOUT=15*60;

//默认的pageSize
retailersConstants.DEFAULT_PAGE_SIZE=10;
//默认的pageSize
retailersConstants.MAX_PAGE_SIZE=99;
//商品平铺显示的数目
retailersConstants.GOODS_TILE_SIZE=3;
//密码找回key
retailersConstants.USER_PASSWORD_TOKER = "retailers_password_token_shopping_";
//新闻列表每页条数
retailersConstants.newsPageSize=5;
//新闻详情页最新文章显示条数
retailersConstants.newsShowCount=5;

//页面显示类型
retailersConstants.GOODS_SHOWTYPE = "goodsShowType";
retailersConstants.NEWS_SHOWTYPE = "newsShowType";

retailersConstants.CONST_SUCCESS = 'SUCCESS';
retailersConstants.CONST_FAILURE = 'FAILURE';

retailersConstants.SINA_SHARES='SINA_SHARES';
module.exports = retailersConstants;